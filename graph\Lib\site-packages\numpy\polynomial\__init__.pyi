from numpy._pytesttester import PytestTester

from numpy.polynomial import (
    ch<PERSON><PERSON><PERSON><PERSON> as cheb<PERSON>he<PERSON>,
    hermite as hermite,
    hermite_e as hermite_e,
    lague<PERSON> as laguerre,
    legendre as legendre,
    polynomial as polynomial,
)
from numpy.polynomial.chebyshev import <PERSON><PERSON><PERSON><PERSON><PERSON> as Ch<PERSON><PERSON>hev
from numpy.polynomial.hermite import Hermite as Hermite
from numpy.polynomial.hermite_e import <PERSON><PERSON><PERSON> as Hermite<PERSON>
from numpy.polynomial.laguerre import <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON>
from numpy.polynomial.legendre import <PERSON><PERSON> as Legend<PERSON>
from numpy.polynomial.polynomial import Polynomial as Polynomial

__all__: list[str]
__path__: list[str]
test: PytestTester

def set_default_printstyle(style): ...
